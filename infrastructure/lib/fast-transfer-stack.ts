import * as cdk from 'aws-cdk-lib';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb';
import * as sqs from 'aws-cdk-lib/aws-sqs';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as autoscaling from 'aws-cdk-lib/aws-autoscaling';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import * as cloudfront from 'aws-cdk-lib/aws-cloudfront';
import * as origins from 'aws-cdk-lib/aws-cloudfront-origins';
import { Construct } from 'constructs';

export class FastTransferStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props?: cdk.StackProps) {
    super(scope, id, props);

    // S3 Buckets
    this.createS3Buckets();
    
    // DynamoDB Tables
    this.createDynamoDBTables();
    
    // SQS Queues
    this.createSQSQueues();
    
    // IAM Roles
    this.createIAMRoles();
    
    // VPC and EC2
    this.createVPCAndEC2();
  }

  private createS3Buckets() {
    // Upload bucket for raw files
    const uploadBucket = new s3.Bucket(this, 'UploadBucket', {
      bucketName: `fasttransfer-upload-${this.account}-${this.region}`,
      versioned: false,
      encryption: s3.BucketEncryption.S3_MANAGED,
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      lifecycleRules: [
        {
          id: 'DeleteUploadsAfter7Days',
          enabled: true,
          expiration: cdk.Duration.days(7),
        },
      ],
      transferAcceleration: true,
      cors: [
        {
          allowedMethods: [
            s3.HttpMethods.GET,
            s3.HttpMethods.POST,
            s3.HttpMethods.PUT,
            s3.HttpMethods.DELETE,
          ],
          allowedOrigins: ['*'], // TODO: Restrict to actual domain
          allowedHeaders: ['*'],
          maxAge: 3000,
        },
      ],
    });

    // Compressed bucket for .zmt files
    const compressedBucket = new s3.Bucket(this, 'CompressedBucket', {
      bucketName: `fasttransfer-compressed-${this.account}-${this.region}`,
      versioned: false,
      encryption: s3.BucketEncryption.S3_MANAGED,
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      lifecycleRules: [
        {
          id: 'DeleteCompressedAfter7Days',
          enabled: true,
          expiration: cdk.Duration.days(7),
        },
      ],
    });

    // Decompressed bucket for serving files via CloudFront
    const decompressedBucket = new s3.Bucket(this, 'DecompressedBucket', {
      bucketName: `fasttransfer-decompressed-${this.account}-${this.region}`,
      versioned: false,
      encryption: s3.BucketEncryption.S3_MANAGED,
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      lifecycleRules: [
        {
          id: 'DeleteDecompressedAfter7Days',
          enabled: true,
          expiration: cdk.Duration.days(7),
        },
      ],
    });

    // Export bucket names for use in other stacks
    new cdk.CfnOutput(this, 'UploadBucketName', {
      value: uploadBucket.bucketName,
      exportName: 'FastTransfer-UploadBucket',
    });

    new cdk.CfnOutput(this, 'CompressedBucketName', {
      value: compressedBucket.bucketName,
      exportName: 'FastTransfer-CompressedBucket',
    });

    new cdk.CfnOutput(this, 'DecompressedBucketName', {
      value: decompressedBucket.bucketName,
      exportName: 'FastTransfer-DecompressedBucket',
    });
  }

  private createDynamoDBTables() {
    // Transfer metadata table
    const transferTable = new dynamodb.Table(this, 'TransferTable', {
      tableName: 'FastTransfer-Transfers',
      partitionKey: {
        name: 'transferId',
        type: dynamodb.AttributeType.STRING,
      },
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      encryption: dynamodb.TableEncryption.AWS_MANAGED,
      pointInTimeRecovery: true,
      timeToLiveAttribute: 'expiresAt',
    });

    // Job tracking table
    const jobTable = new dynamodb.Table(this, 'JobTable', {
      tableName: 'FastTransfer-Jobs',
      partitionKey: {
        name: 'jobId',
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: {
        name: 'createdAt',
        type: dynamodb.AttributeType.NUMBER,
      },
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      encryption: dynamodb.TableEncryption.AWS_MANAGED,
      pointInTimeRecovery: true,
    });

    // Add GSI for querying jobs by status
    jobTable.addGlobalSecondaryIndex({
      indexName: 'StatusIndex',
      partitionKey: {
        name: 'status',
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: {
        name: 'createdAt',
        type: dynamodb.AttributeType.NUMBER,
      },
    });

    new cdk.CfnOutput(this, 'TransferTableName', {
      value: transferTable.tableName,
      exportName: 'FastTransfer-TransferTable',
    });

    new cdk.CfnOutput(this, 'JobTableName', {
      value: jobTable.tableName,
      exportName: 'FastTransfer-JobTable',
    });
  }

  private createSQSQueues() {
    // Dead letter queue for failed jobs
    const dlq = new sqs.Queue(this, 'JobDLQ', {
      queueName: 'FastTransfer-JobDLQ',
      encryption: sqs.QueueEncryption.SQS_MANAGED,
    });

    // Compression job queue
    const compressionQueue = new sqs.Queue(this, 'CompressionQueue', {
      queueName: 'FastTransfer-CompressionJobs',
      encryption: sqs.QueueEncryption.SQS_MANAGED,
      visibilityTimeout: cdk.Duration.minutes(15), // Allow time for compression
      deadLetterQueue: {
        queue: dlq,
        maxReceiveCount: 3,
      },
    });

    // Decompression job queue
    const decompressionQueue = new sqs.Queue(this, 'DecompressionQueue', {
      queueName: 'FastTransfer-DecompressionJobs',
      encryption: sqs.QueueEncryption.SQS_MANAGED,
      visibilityTimeout: cdk.Duration.minutes(10), // Allow time for decompression
      deadLetterQueue: {
        queue: dlq,
        maxReceiveCount: 3,
      },
    });

    new cdk.CfnOutput(this, 'CompressionQueueUrl', {
      value: compressionQueue.queueUrl,
      exportName: 'FastTransfer-CompressionQueue',
    });

    new cdk.CfnOutput(this, 'DecompressionQueueUrl', {
      value: decompressionQueue.queueUrl,
      exportName: 'FastTransfer-DecompressionQueue',
    });
  }

  private createIAMRoles() {
    // Lambda execution role
    const lambdaRole = new iam.Role(this, 'LambdaExecutionRole', {
      roleName: 'FastTransfer-LambdaExecutionRole',
      assumedBy: new iam.ServicePrincipal('lambda.amazonaws.com'),
      managedPolicies: [
        iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaBasicExecutionRole'),
      ],
      inlinePolicies: {
        S3Access: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: [
                's3:GetObject',
                's3:PutObject',
                's3:DeleteObject',
                's3:ListBucket',
              ],
              resources: [
                `arn:aws:s3:::fasttransfer-*-${this.account}-${this.region}`,
                `arn:aws:s3:::fasttransfer-*-${this.account}-${this.region}/*`,
              ],
            }),
          ],
        }),
        DynamoDBAccess: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: [
                'dynamodb:GetItem',
                'dynamodb:PutItem',
                'dynamodb:UpdateItem',
                'dynamodb:DeleteItem',
                'dynamodb:Query',
                'dynamodb:Scan',
              ],
              resources: [
                `arn:aws:dynamodb:${this.region}:${this.account}:table/FastTransfer-*`,
              ],
            }),
          ],
        }),
        SQSAccess: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: [
                'sqs:SendMessage',
                'sqs:ReceiveMessage',
                'sqs:DeleteMessage',
                'sqs:GetQueueAttributes',
              ],
              resources: [
                `arn:aws:sqs:${this.region}:${this.account}:FastTransfer-*`,
              ],
            }),
          ],
        }),
      },
    });

    new cdk.CfnOutput(this, 'LambdaRoleArn', {
      value: lambdaRole.roleArn,
      exportName: 'FastTransfer-LambdaRole',
    });
  }

  private createVPCAndEC2() {
    // VPC for EC2 workers
    const vpc = new ec2.Vpc(this, 'FastTransferVPC', {
      maxAzs: 2,
      natGateways: 1, // Cost optimization - use 1 NAT gateway
      subnetConfiguration: [
        {
          cidrMask: 24,
          name: 'Public',
          subnetType: ec2.SubnetType.PUBLIC,
        },
        {
          cidrMask: 24,
          name: 'Private',
          subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
        },
      ],
    });

    // Security group for EC2 workers
    const workerSecurityGroup = new ec2.SecurityGroup(this, 'WorkerSecurityGroup', {
      vpc,
      description: 'Security group for FastTransfer EC2 workers',
      allowAllOutbound: true,
    });

    // EC2 role for workers
    const ec2Role = new iam.Role(this, 'EC2WorkerRole', {
      assumedBy: new iam.ServicePrincipal('ec2.amazonaws.com'),
      managedPolicies: [
        iam.ManagedPolicy.fromAwsManagedPolicyName('AmazonSSMManagedInstanceCore'),
      ],
      inlinePolicies: {
        WorkerAccess: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: [
                's3:GetObject',
                's3:PutObject',
                's3:DeleteObject',
              ],
              resources: [
                `arn:aws:s3:::fasttransfer-*-${this.account}-${this.region}/*`,
              ],
            }),
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: [
                'sqs:ReceiveMessage',
                'sqs:DeleteMessage',
                'sqs:GetQueueAttributes',
              ],
              resources: [
                `arn:aws:sqs:${this.region}:${this.account}:FastTransfer-*`,
              ],
            }),
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: [
                'dynamodb:UpdateItem',
                'dynamodb:PutItem',
              ],
              resources: [
                `arn:aws:dynamodb:${this.region}:${this.account}:table/FastTransfer-*`,
              ],
            }),
          ],
        }),
      },
    });

    const instanceProfile = new iam.CfnInstanceProfile(this, 'EC2InstanceProfile', {
      roles: [ec2Role.roleName],
    });

    new cdk.CfnOutput(this, 'VPCId', {
      value: vpc.vpcId,
      exportName: 'FastTransfer-VPC',
    });

    new cdk.CfnOutput(this, 'WorkerSecurityGroupId', {
      value: workerSecurityGroup.securityGroupId,
      exportName: 'FastTransfer-WorkerSecurityGroup',
    });

    new cdk.CfnOutput(this, 'EC2RoleArn', {
      value: ec2Role.roleArn,
      exportName: 'FastTransfer-EC2Role',
    });

    // Launch template for EC2 workers
    const launchTemplate = new ec2.LaunchTemplate(this, 'WorkerLaunchTemplate', {
      launchTemplateName: 'FastTransfer-Worker',
      instanceType: ec2.InstanceType.of(ec2.InstanceClass.C5, ec2.InstanceSize.LARGE),
      machineImage: ec2.MachineImage.latestAmazonLinux2({
        edition: ec2.AmazonLinuxEdition.STANDARD,
        virtualization: ec2.AmazonLinuxVirt.HVM,
        storage: ec2.AmazonLinuxStorage.GENERAL_PURPOSE,
      }),
      securityGroup: workerSecurityGroup,
      role: ec2Role,
      userData: ec2.UserData.forLinux(),
      blockDevices: [
        {
          deviceName: '/dev/xvda',
          volume: ec2.BlockDeviceVolume.ebs(20, {
            volumeType: ec2.EbsDeviceVolumeType.GP3,
            encrypted: true,
          }),
        },
      ],
    });

    // Add user data script for ZMT setup
    launchTemplate.userData.addCommands(
      '#!/bin/bash',
      'yum update -y',
      'yum install -y nodejs npm python3 python3-pip',

      // Install AWS CLI v2
      'curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"',
      'unzip awscliv2.zip',
      'sudo ./aws/install',

      // Create application directory
      'mkdir -p /opt/fasttransfer',
      'cd /opt/fasttransfer',

      // Download and setup ZMT (placeholder - actual ZMT binary would be downloaded here)
      'echo "#!/bin/bash" > /opt/fasttransfer/zmt',
      'echo "# ZMT compression tool placeholder" >> /opt/fasttransfer/zmt',
      'echo "# Usage: ./zmt [compress|extract] input output" >> /opt/fasttransfer/zmt',
      'chmod +x /opt/fasttransfer/zmt',

      // Install Node.js dependencies for worker service
      'cat > package.json << EOF',
      '{',
      '  "name": "fasttransfer-worker",',
      '  "version": "1.0.0",',
      '  "main": "worker.js",',
      '  "dependencies": {',
      '    "aws-sdk": "^2.1400.0",',
      '    "@aws-sdk/client-s3": "^3.400.0",',
      '    "@aws-sdk/client-sqs": "^3.400.0",',
      '    "@aws-sdk/client-dynamodb": "^3.400.0"',
      '  }',
      '}',
      'EOF',

      'npm install',

      // Create systemd service
      'cat > /etc/systemd/system/fasttransfer-worker.service << EOF',
      '[Unit]',
      'Description=FastTransfer Worker Service',
      'After=network.target',
      '',
      '[Service]',
      'Type=simple',
      'User=ec2-user',
      'WorkingDirectory=/opt/fasttransfer',
      'ExecStart=/usr/bin/node worker.js',
      'Restart=always',
      'RestartSec=10',
      'Environment=NODE_ENV=production',
      `Environment=AWS_REGION=${this.region}`,
      '',
      '[Install]',
      'WantedBy=multi-user.target',
      'EOF',

      // Enable and start the service
      'systemctl daemon-reload',
      'systemctl enable fasttransfer-worker',
      'systemctl start fasttransfer-worker',

      // Signal CloudFormation that the instance is ready
      '/opt/aws/bin/cfn-signal -e $? --stack ${AWS::StackName} --resource AutoScalingGroup --region ${AWS::Region}'
    );

    new cdk.CfnOutput(this, 'LaunchTemplateId', {
      value: launchTemplate.launchTemplateId!,
      exportName: 'FastTransfer-LaunchTemplate',
    });
  }
}
