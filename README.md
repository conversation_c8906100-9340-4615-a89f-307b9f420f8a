# FastTransfer

A high-performance file transfer platform similar to WeTransfer, featuring ZMT compression and AWS-native architecture for optimal speed and cost efficiency.

## Features

- **High-Speed Transfers**: AWS S3 Transfer Acceleration and CloudFront CDN
- **ZMT Compression**: Advanced compression on EC2 Ubuntu instances
- **Scalable Architecture**: Auto-scaling EC2 workers and serverless APIs
- **Secure**: HTTPS, pre-signed URLs, and encryption at rest
- **Global**: Optimized for worldwide file transfers

## Architecture

```
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│   Frontend  │───▶│  API Gateway │───▶│   Lambda    │
│   (React)   │    │              │    │ Functions   │
└─────────────┘    └──────────────┘    └─────────────┘
                                              │
                   ┌──────────────┐          │
                   │  CloudFront  │          ▼
                   │     CDN      │    ┌─────────────┐
                   └──────────────┘    │ DynamoDB /  │
                          │            │    SQS      │
                          ▼            └─────────────┘
                   ┌──────────────┐          │
                   │      S3      │          ▼
                   │   Buckets    │    ┌─────────────┐
                   └──────────────┘    │EC2 Workers  │
                                       │(ZMT Compression)│
                                       └─────────────┘
```

## Project Structure

```
fast-transfer/
├── packages/
│   └── frontend/          # React web application
├── services/
│   ├── backend/           # Lambda functions
│   └── worker/            # EC2 worker services
├── infrastructure/        # AWS CDK/CloudFormation
├── docs/                  # Documentation
└── scripts/              # Deployment and utility scripts
```

## Quick Start

1. **Prerequisites**
   - Node.js 18+
   - AWS CLI configured
   - Docker (for local development)

2. **Installation**
   ```bash
   npm install
   ```

3. **Development**
   ```bash
   npm run dev
   ```

4. **Deployment**
   ```bash
   npm run deploy:infra
   npm run deploy:backend
   npm run deploy:frontend
   ```

## Development Workflow

1. **Infrastructure**: Deploy AWS resources first
2. **Backend**: Deploy Lambda functions and APIs
3. **Frontend**: Build and deploy React application
4. **Workers**: Configure EC2 instances with ZMT

## Key Components

- **Upload Flow**: Multipart upload → S3 → SQS → EC2 compression
- **Download Flow**: Link validation → Decompression → CloudFront delivery
- **Compression**: ZMT binary on Ubuntu EC2 instances
- **Monitoring**: CloudWatch metrics and alarms

## Performance Targets

- Upload speed: Optimized with S3 Transfer Acceleration
- Compression ratio: Target 80% size reduction
- Global delivery: <2s first byte via CloudFront
- Scalability: Auto-scaling based on queue depth

## Security

- All transfers over HTTPS
- Pre-signed URLs for access control
- IAM roles with least privilege
- Encryption at rest (AES-256)

## License

MIT License - see LICENSE file for details
